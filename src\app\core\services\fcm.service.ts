import { Injectable, signal } from '@angular/core';
import { Observable, BehaviorSubject, from } from 'rxjs';
import { Messaging, getToken, onMessage, MessagePayload } from '@angular/fire/messaging';
import { Firestore, collection, doc, addDoc, updateDoc, query, where, getDocs, Timestamp } from '@angular/fire/firestore';
import { environment } from '@environments/environment';

export interface FCMToken {
  id?: string;
  token: string;
  userId: string;
  churchId: string;
  deviceType: 'web' | 'android' | 'ios';
  isActive: boolean;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface NotificationPayload {
  title: string;
  body: string;
  icon?: string;
  image?: string;
  data?: Record<string, any>;
}

export interface SendNotificationRequest {
  title: string;
  body: string;
  churchId: string;
  targetUserIds?: string[];
  targetTokens?: string[];
  data?: Record<string, any>;
  icon?: string;
  image?: string;
}

export interface NotificationResult {
  success: boolean;
  successCount: number;
  failureCount: number;
  errors: string[];
  messageId?: string;
}

@Injectable({
  providedIn: 'root'
})
export class FCMService {
  // Reactive state
  isLoading = signal<boolean>(false);
  error = signal<string | null>(null);
  currentToken = signal<string | null>(null);

  private messageSubject = new BehaviorSubject<MessagePayload | null>(null);
  public message$ = this.messageSubject.asObservable();

  constructor(
    private messaging: Messaging,
    private firestore: Firestore
  ) {
    this.initializeMessaging();
  }

  /**
   * Initialize Firebase Cloud Messaging
   */
  private initializeMessaging(): void {
    // Listen for foreground messages
    onMessage(this.messaging, (payload) => {
      console.log('Message received in foreground:', payload);
      this.messageSubject.next(payload);
      this.showNotification(payload);
    });
  }

  /**
   * Request notification permission and get FCM token
   */
  async requestPermission(userId: string, churchId: string): Promise<string | null> {
    try {
      this.isLoading.set(true);
      this.error.set(null);

      // Request permission
      const permission = await Notification.requestPermission();
      if (permission !== 'granted') {
        throw new Error('Notification permission denied');
      }

      // Get FCM token
      const token = await getToken(this.messaging, {
        vapidKey: environment.firebase.vapidKey // You'll need to add this to environment
      });

      if (token) {
        this.currentToken.set(token);
        await this.saveTokenToFirestore(token, userId, churchId);
        console.log('FCM token obtained and saved:', token);
        return token;
      } else {
        throw new Error('No registration token available');
      }
    } catch (error: any) {
      console.error('Error getting FCM token:', error);
      this.error.set(error.message);
      return null;
    } finally {
      this.isLoading.set(false);
    }
  }

  /**
   * Save FCM token to Firestore
   */
  private async saveTokenToFirestore(token: string, userId: string, churchId: string): Promise<void> {
    const tokensCollection = collection(this.firestore, 'fcmTokens');

    // Check if token already exists
    const existingTokenQuery = query(
      tokensCollection,
      where('token', '==', token),
      where('userId', '==', userId)
    );

    const existingTokens = await getDocs(existingTokenQuery);

    if (existingTokens.empty) {
      // Create new token document
      const tokenData: Omit<FCMToken, 'id'> = {
        token,
        userId,
        churchId,
        deviceType: 'web',
        isActive: true,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      };

      await addDoc(tokensCollection, tokenData);
    } else {
      // Update existing token
      const existingDoc = existingTokens.docs[0];
      await updateDoc(existingDoc.ref, {
        isActive: true,
        updatedAt: Timestamp.now()
      });
    }
  }

  /**
   * Get all active FCM tokens for a church
   */
  async getChurchTokens(churchId: string): Promise<FCMToken[]> {
    const tokensCollection = collection(this.firestore, 'fcmTokens');
    const tokensQuery = query(
      tokensCollection,
      where('churchId', '==', churchId),
      where('isActive', '==', true)
    );

    const querySnapshot = await getDocs(tokensQuery);
    const tokens: FCMToken[] = [];

    querySnapshot.forEach(doc => {
      tokens.push({
        id: doc.id,
        ...doc.data() as Omit<FCMToken, 'id'>
      });
    });

    return tokens;
  }

  /**
   * Get FCM tokens for specific users
   */
  async getUserTokens(userIds: string[]): Promise<FCMToken[]> {
    if (userIds.length === 0) return [];

    const tokensCollection = collection(this.firestore, 'fcmTokens');
    const tokensQuery = query(
      tokensCollection,
      where('userId', 'in', userIds),
      where('isActive', '==', true)
    );

    const querySnapshot = await getDocs(tokensQuery);
    const tokens: FCMToken[] = [];

    querySnapshot.forEach(doc => {
      tokens.push({
        id: doc.id,
        ...doc.data() as Omit<FCMToken, 'id'>
      });
    });

    return tokens;
  }

  /**
   * Send notification using Firebase Cloud Functions
   * Note: This requires a Cloud Function to actually send the notifications
   */
  async sendNotification(request: SendNotificationRequest): Promise<NotificationResult> {
    try {
      this.isLoading.set(true);
      this.error.set(null);

      // Get target tokens
      let targetTokens: string[] = [];

      if (request.targetTokens) {
        targetTokens = request.targetTokens;
      } else if (request.targetUserIds) {
        const userTokens = await this.getUserTokens(request.targetUserIds);
        targetTokens = userTokens.map(t => t.token);
      } else {
        // Send to all church members
        const churchTokens = await this.getChurchTokens(request.churchId);
        targetTokens = churchTokens.map(t => t.token);
      }

      if (targetTokens.length === 0) {
        return {
          success: false,
          successCount: 0,
          failureCount: 0,
          errors: ['No valid tokens found for notification']
        };
      }

      // Save notification to Firestore for history
      const notificationData = {
        title: request.title,
        body: request.body,
        churchId: request.churchId,
        targetTokens,
        data: request.data || {},
        icon: request.icon,
        image: request.image,
        status: 'pending',
        createdAt: Timestamp.now()
      };

      const notificationsCollection = collection(this.firestore, 'notifications');
      const notificationDoc = await addDoc(notificationsCollection, notificationData);

      // TODO: Call Cloud Function to send the actual notification
      // For now, we'll simulate success
      const result: NotificationResult = {
        success: true,
        successCount: targetTokens.length,
        failureCount: 0,
        errors: [],
        messageId: notificationDoc.id
      };

      // Update notification status
      await updateDoc(notificationDoc, {
        status: 'sent',
        result,
        sentAt: Timestamp.now()
      });

      return result;
    } catch (error: any) {
      console.error('Error sending notification:', error);
      this.error.set(error.message);
      return {
        success: false,
        successCount: 0,
        failureCount: 1,
        errors: [error.message]
      };
    } finally {
      this.isLoading.set(false);
    }
  }

  /**
   * Show browser notification
   */
  private showNotification(payload: MessagePayload): void {
    if ('Notification' in window && Notification.permission === 'granted') {
      const notification = new Notification(payload.notification?.title || 'New Message', {
        body: payload.notification?.body,
        icon: payload.notification?.icon || '/favicon.ico',
        data: payload.data
      });

      notification.onclick = () => {
        window.focus();
        notification.close();
        // Handle notification click
        if (payload.data && payload.data['url']) {
          window.location.href = payload.data['url'];
        }
      };
    }
  }

  /**
   * Deactivate FCM token
   */
  async deactivateToken(token: string): Promise<void> {
    const tokensCollection = collection(this.firestore, 'fcmTokens');
    const tokenQuery = query(tokensCollection, where('token', '==', token));

    const querySnapshot = await getDocs(tokenQuery);
    querySnapshot.forEach(async (doc) => {
      await updateDoc(doc.ref, {
        isActive: false,
        updatedAt: Timestamp.now()
      });
    });
  }

  /**
   * Clear current token
   */
  clearToken(): void {
    this.currentToken.set(null);
  }
}
