import { Component, OnInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDividerModule } from '@angular/material/divider';
import { MatChipsModule } from '@angular/material/chips';
import { MatBadgeModule } from '@angular/material/badge';
import { MatTabsModule } from '@angular/material/tabs';

import { NotificationsService, Notification, NotificationType, NotificationPriority } from '../../core/services/notifications.service';

@Component({
  selector: 'app-notifications',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule,
    MatSelectModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatDialogModule,
    MatTooltipModule,
    MatDividerModule,
    MatChipsModule,
    MatBadgeModule,
    MatTabsModule,
  ],
  template: `
    <div class="notifications-container">
      <!-- Page Header -->
      <div class="page-header">
        <h1>
          <mat-icon>notifications</mat-icon>
          Notifications Management
        </h1>
        <p>Send announcements and manage communication with your church members</p>
      </div>

      <mat-card class="send-notification-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>notifications</mat-icon>
            Send Notification
          </mat-card-title>
          <mat-card-subtitle>Send announcements to church members</mat-card-subtitle>
        </mat-card-header>
        
        <mat-card-content>
          <form [formGroup]="notificationForm" (ngSubmit)="onSendNotification()">
            <div class="form-row">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Notification Title</mat-label>
                <input matInput formControlName="title" placeholder="Enter notification title">
                <mat-error *ngIf="notificationForm.get('title')?.hasError('required')">
                  Title is required
                </mat-error>
              </mat-form-field>
            </div>

            <div class="form-row">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Message</mat-label>
                <textarea matInput formControlName="message" rows="4" placeholder="Enter your message"></textarea>
                <mat-error *ngIf="notificationForm.get('message')?.hasError('required')">
                  Message is required
                </mat-error>
              </mat-form-field>
            </div>

            <div class="form-row">
              <mat-form-field appearance="outline" class="half-width">
                <mat-label>Type</mat-label>
                <mat-select formControlName="type">
                  <mat-option value="church_announcement">Church Announcement</mat-option>
                  <mat-option value="service_schedule">Service Schedule</mat-option>
                  <mat-option value="attendance_reminder">Attendance Reminder</mat-option>
                </mat-select>
              </mat-form-field>

              <mat-form-field appearance="outline" class="half-width">
                <mat-label>Priority</mat-label>
                <mat-select formControlName="priority">
                  <mat-option value="low">Low</mat-option>
                  <mat-option value="normal">Normal</mat-option>
                  <mat-option value="high">High</mat-option>
                  <mat-option value="urgent">Urgent</mat-option>
                </mat-select>
              </mat-form-field>
            </div>

            <div class="form-actions">
              <button mat-raised-button color="primary" type="submit" 
                      [disabled]="notificationForm.invalid || isLoading()">
                <mat-icon>send</mat-icon>
                Send Notification
                <mat-spinner *ngIf="isLoading()" diameter="20" class="spinner"></mat-spinner>
              </button>
            </div>
          </form>
        </mat-card-content>
      </mat-card>

      <mat-card class="notification-history-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>history</mat-icon>
            Notification History
          </mat-card-title>
          <mat-card-subtitle>Recent notifications sent to members</mat-card-subtitle>
        </mat-card-header>
        
        <mat-card-content>
          <div *ngIf="isLoading()" class="loading-container">
            <mat-spinner diameter="40"></mat-spinner>
            <p>Loading notifications...</p>
          </div>

          <div *ngIf="!isLoading() && notifications().length === 0" class="empty-state">
            <mat-icon>notifications_none</mat-icon>
            <h3>No notifications sent yet</h3>
            <p>Send your first notification to church members using the form above.</p>
          </div>

          <div *ngIf="!isLoading() && notifications().length > 0" class="notifications-list">
            <div *ngFor="let notification of notifications()" class="notification-item">
              <div class="notification-header">
                <h4>{{ notification.title }}</h4>
                <div class="notification-meta">
                  <mat-chip [color]="getTypeColor(notification.type)" selected>
                    {{ getTypeLabel(notification.type) }}
                  </mat-chip>
                  <mat-chip [color]="getPriorityColor(notification.priority)" selected>
                    {{ getPriorityLabel(notification.priority) }}
                  </mat-chip>
                </div>
              </div>
              
              <p class="notification-message">{{ notification.message }}</p>
              
              <div class="notification-stats">
                <div class="stat">
                  <mat-icon>people</mat-icon>
                  <span>{{ notification.recipientCount }} recipients</span>
                </div>
                <div class="stat success">
                  <mat-icon>check_circle</mat-icon>
                  <span>{{ notification.deliveredCount }} delivered</span>
                </div>
                <div class="stat error" *ngIf="notification.failedCount > 0">
                  <mat-icon>error</mat-icon>
                  <span>{{ notification.failedCount }} failed</span>
                </div>
              </div>
              
              <div class="notification-footer">
                <span class="timestamp">{{ formatDate(notification.createdAt) }}</span>
                <span class="sender" *ngIf="notification.sender">
                  by {{ notification.sender.firstName }} {{ notification.sender.lastName }}
                </span>
              </div>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .notifications-container {
      padding: 20px;
      max-width: 1200px;
      margin: 0 auto;
    }

    .page-header {
      margin-bottom: 32px;
    }

    .page-header h1 {
      display: flex;
      align-items: center;
      gap: 12px;
      margin: 0 0 8px 0;
      font-size: 2rem;
      font-weight: 500;
      color: #333;
    }

    .page-header p {
      margin: 0;
      color: #666;
      font-size: 1rem;
    }

    .send-notification-card,
    .notification-history-card {
      margin-bottom: 24px;
    }

    .form-row {
      display: flex;
      gap: 16px;
      margin-bottom: 16px;
    }

    .full-width {
      width: 100%;
    }

    .half-width {
      flex: 1;
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      margin-top: 24px;
    }

    .spinner {
      margin-left: 8px;
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 40px;
    }

    .empty-state {
      text-align: center;
      padding: 40px;
      color: #666;
    }

    .empty-state mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      margin-bottom: 16px;
    }

    .notifications-list {
      max-height: 600px;
      overflow-y: auto;
    }

    .notification-item {
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      padding: 16px;
      margin-bottom: 16px;
      background: #fafafa;
    }

    .notification-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 8px;
    }

    .notification-header h4 {
      margin: 0;
      font-weight: 500;
    }

    .notification-meta {
      display: flex;
      gap: 8px;
    }

    .notification-message {
      margin: 8px 0 16px 0;
      color: #666;
    }

    .notification-stats {
      display: flex;
      gap: 16px;
      margin-bottom: 12px;
    }

    .stat {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 14px;
    }

    .stat.success {
      color: #4caf50;
    }

    .stat.error {
      color: #f44336;
    }

    .notification-footer {
      display: flex;
      justify-content: space-between;
      font-size: 12px;
      color: #999;
    }

    mat-card-header mat-icon {
      margin-right: 8px;
    }
  `]
})
export class NotificationsComponent implements OnInit {
  notificationForm: FormGroup;
  notifications = signal<Notification[]>([]);
  isLoading = signal<boolean>(false);

  constructor(
    private fb: FormBuilder,
    private notificationsService: NotificationsService,
    private snackBar: MatSnackBar
  ) {
    this.notificationForm = this.fb.group({
      title: ['', Validators.required],
      message: ['', Validators.required],
      type: ['church_announcement', Validators.required],
      priority: ['normal', Validators.required]
    });
  }

  ngOnInit(): void {
    this.loadNotifications();
  }

  onSendNotification(): void {
    console.log('🔔 Send notification clicked');
    console.log('Form valid:', this.notificationForm.valid);
    console.log('Form value:', this.notificationForm.value);
    console.log('Form errors:', this.notificationForm.errors);

    // Only mark as touched if form is invalid
    if (!this.notificationForm.valid) {
      this.notificationForm.markAllAsTouched();
    }

    if (this.notificationForm.valid) {
      const formData = this.notificationForm.value;
      console.log('📤 Sending notification with data:', formData);

      this.notificationsService.sendNotification({
        title: formData.title,
        message: formData.message,
        type: formData.type as NotificationType,
        priority: formData.priority as NotificationPriority
      }).subscribe({
        next: (response) => {
          console.log('✅ Notification sent successfully:', response);
          this.snackBar.open(
            `Notification sent to ${response.result.successCount} members`,
            'Close',
            { duration: 5000 }
          );
          // Reset form on success and clear validation errors
          setTimeout(() => {
            this.notificationForm.reset({
              title: '',
              message: '',
              type: 'church_announcement',
              priority: 'normal'
            });
            // Clear all validation errors
            this.notificationForm.markAsUntouched();
            this.notificationForm.markAsPristine();
          }, 100);
          this.loadNotifications();
        },
        error: (error) => {
          console.error('❌ Notification error:', error);
          console.error('Error details:', {
            status: error.status,
            statusText: error.statusText,
            error: error.error,
            message: error.message
          });

          let errorMessage = 'Failed to send notification';
          if (error.error?.message) {
            errorMessage = error.error.message;
          } else if (error.message) {
            errorMessage = error.message;
          } else if (error.status) {
            errorMessage = `Server error (${error.status}): ${error.statusText}`;
          }

          this.snackBar.open(errorMessage, 'Close', { duration: 8000 });
          // Don't reset form on error so user can fix and retry
        }
      });
    } else {
      console.log('❌ Form is invalid');
      Object.keys(this.notificationForm.controls).forEach(key => {
        const control = this.notificationForm.get(key);
        if (control && control.errors) {
          console.log(`Field ${key} errors:`, control.errors);
        }
      });
      this.snackBar.open('Please fill in all required fields', 'Close', { duration: 3000 });
    }
  }

  private loadNotifications(): void {
    this.notificationsService.getNotifications().subscribe({
      next: (response) => {
        this.notifications.set(response.notifications);
      },
      error: (error) => {
        console.error('Error loading notifications:', error);
      }
    });
  }

  getTypeColor(type: NotificationType): string {
    switch (type) {
      case NotificationType.SERVICE_SCHEDULE: return 'primary';
      case NotificationType.CHURCH_ANNOUNCEMENT: return 'accent';
      case NotificationType.ATTENDANCE_REMINDER: return 'warn';
      default: return '';
    }
  }

  getTypeLabel(type: NotificationType): string {
    switch (type) {
      case NotificationType.SERVICE_SCHEDULE: return 'Service Schedule';
      case NotificationType.CHURCH_ANNOUNCEMENT: return 'Announcement';
      case NotificationType.ATTENDANCE_REMINDER: return 'Attendance';
      default: return type;
    }
  }

  getPriorityColor(priority: NotificationPriority): string {
    switch (priority) {
      case NotificationPriority.URGENT: return 'warn';
      case NotificationPriority.HIGH: return 'accent';
      default: return '';
    }
  }

  getPriorityLabel(priority: NotificationPriority): string {
    return priority.charAt(0).toUpperCase() + priority.slice(1);
  }

  formatDate(date: Date): string {
    return new Date(date).toLocaleString();
  }
}
